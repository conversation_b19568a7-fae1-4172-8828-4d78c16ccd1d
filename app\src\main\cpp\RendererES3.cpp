#include <thread>

#include <string>
#include <cstdint>
#include <vector>
#include "gles3jni.h"
#include <EGL/egl.h>
#include "imGui/imgui.h"
#include "imGui/imgui_impl_android_gl2.h"
#include "../KittyMemoryEx/KittyMemoryMgr.hpp"
KittyMemoryMgr kittyMemMgr;

#define STR(s) #s
#define STRV(s) STR(s)

#define POS_ATTRIB 0
#define COLOR_ATTRIB 1
#define SCALEROT_ATTRIB 2
#define OFFSET_ATTRIB 3

bool g_Initialize = false;
const std::string PackageName = "com.example.unity3d";
bool useShizuku = true; // Flag untuk menggunakan Shizuku

static const char VERTEX_SHADER[] =
    "#version 300 es\n"
    "layout(location = " STRV(POS_ATTRIB) ") in vec2 pos;\n"
    "layout(location=" STRV(COLOR_ATTRIB) ") in vec4 color;\n"
    "layout(location=" STRV(SCALEROT_ATTRIB) ") in vec4 scaleRot;\n"
    "layout(location=" STRV(OFFSET_ATTRIB) ") in vec2 offset;\n"
    "out vec4 vColor;\n"
    "void main() {\n"
    "    mat2 sr = mat2(scaleRot.xy, scaleRot.zw);\n"
    "    gl_Position = vec4(sr*pos + offset, 0.0, 1.0);\n"
    "    vColor = color;\n"
    "}\n";

static const char FRAGMENT_SHADER[] =
    "#version 300 es\n"
    "precision mediump float;\n"
    "in vec4 vColor;\n"
    "out vec4 outColor;\n"
    "void main() {\n"
    "    outColor = vColor;\n"
    "}\n";

class RendererES3: public Renderer {
public:
    RendererES3();
    virtual ~RendererES3();
    bool init();

private:
    enum {VB_INSTANCE, VB_SCALEROT, VB_OFFSET, VB_COUNT};

    virtual float* mapOffsetBuf();
    virtual void unmapOffsetBuf();
    virtual float* mapTransformBuf();
    virtual void unmapTransformBuf();
    virtual void draw(unsigned int numInstances);

    const EGLContext mEglContext;
    GLuint mProgram;
    GLuint mVB[VB_COUNT];
    GLuint mVBState;

    // KittyMemory variables
    uintptr_t il2cppBase;
    MemoryPatch get_canShoot;
    MemoryPatch get_gold;
    bool canShootEnabled;
    bool isDumped;
};

Renderer* createES3Renderer() {
    RendererES3* renderer = new RendererES3;
    if (!renderer->init()) {
        delete renderer;
        return NULL;
    }
    return renderer;
}

RendererES3::RendererES3()
:   mEglContext(eglGetCurrentContext()),
    mProgram(0),
    mVBState(0),
    il2cppBase(0),
    canShootEnabled(false),
    isDumped(false)
{
    for (int i = 0; i < VB_COUNT; i++)
        mVB[i] = 0;
}

bool RendererES3::init() {
    mProgram = createProgram(VERTEX_SHADER, FRAGMENT_SHADER);
    if (!mProgram)
        return false;

	pid_t processID = KittyMemoryEx::getProcessID(PackageName);
    if (!processID)
    {
        KITTY_LOGI("Couldn't find process id of %s.", PackageName.c_str());
        return false;
    }

    std::string processName = KittyMemoryEx::getProcessName(processID);
    KITTY_LOGI("Process Name: %s", processName.c_str());
    KITTY_LOGI("Process ID: %d", processID);

    // initialize KittyMemoryMgr instance with process ID
    if (!kittyMemMgr.initialize(processID, EK_MEM_OP_SYSCALL, true))
    {
        KITTY_LOGI("Error occurred initializing KittyMemoryMgr");
        return false;
    }

	ElfScanner g_il2cppElf{};
    // loop until our target library is found
    do
    {
        sleep(1);

        // get loaded elf
        g_il2cppElf = kittyMemMgr.getMemElf("libil2cpp.so");

        // in case lib is loaded from config apk
        for (auto& it : KittyMemoryEx::getAllMaps(kittyMemMgr.processID()))
        {
            if (KittyUtils::String::Contains(it.pathname, kittyMemMgr.processName()) && KittyUtils::String::EndsWith(it.pathname, ".apk"))
            {
                g_il2cppElf = kittyMemMgr.getMemElfInZip(it.pathname, "libil2cpp.so");
                if (g_il2cppElf.isValid()) break;
            }
        }

    } while (!g_il2cppElf.isValid());

    il2cppBase = g_il2cppElf.base();
    KITTY_LOGI("libil2cpp.so base: %p", (void *)il2cppBase);

    glGenBuffers(VB_COUNT, mVB);
    glBindBuffer(GL_ARRAY_BUFFER, mVB[VB_INSTANCE]);
    glBufferData(GL_ARRAY_BUFFER, sizeof(QUAD), &QUAD[0], GL_STATIC_DRAW);
    glBindBuffer(GL_ARRAY_BUFFER, mVB[VB_SCALEROT]);
    glBufferData(GL_ARRAY_BUFFER, MAX_INSTANCES * 4*sizeof(float), NULL, GL_DYNAMIC_DRAW);
    glBindBuffer(GL_ARRAY_BUFFER, mVB[VB_OFFSET]);
    glBufferData(GL_ARRAY_BUFFER, MAX_INSTANCES * 2*sizeof(float), NULL, GL_STATIC_DRAW);

    glGenVertexArrays(1, &mVBState);
    glBindVertexArray(mVBState);

    glBindBuffer(GL_ARRAY_BUFFER, mVB[VB_INSTANCE]);
    glVertexAttribPointer(POS_ATTRIB, 2, GL_FLOAT, GL_FALSE, sizeof(Vertex), (const GLvoid*)offsetof(Vertex, pos));
    glVertexAttribPointer(COLOR_ATTRIB, 4, GL_UNSIGNED_BYTE, GL_TRUE, sizeof(Vertex), (const GLvoid*)offsetof(Vertex, rgba));
    glEnableVertexAttribArray(POS_ATTRIB);
    glEnableVertexAttribArray(COLOR_ATTRIB);

    glBindBuffer(GL_ARRAY_BUFFER, mVB[VB_SCALEROT]);
    glVertexAttribPointer(SCALEROT_ATTRIB, 4, GL_FLOAT, GL_FALSE, 4*sizeof(float), 0);
    glEnableVertexAttribArray(SCALEROT_ATTRIB);
    glVertexAttribDivisor(SCALEROT_ATTRIB, 1);

    glBindBuffer(GL_ARRAY_BUFFER, mVB[VB_OFFSET]);
    glVertexAttribPointer(OFFSET_ATTRIB, 2, GL_FLOAT, GL_FALSE, 2*sizeof(float), 0);
    glEnableVertexAttribArray(OFFSET_ATTRIB);
    glVertexAttribDivisor(OFFSET_ATTRIB, 1);

    ALOGV("Using OpenGL ES 3.0 renderer");
    ImGui_ImplAndroidGL3_Init();
    return true;
}

RendererES3::~RendererES3() {
    if (eglGetCurrentContext() != mEglContext)
        return;
    ImGui_ImplAndroidGL3_Shutdown();
    glDeleteVertexArrays(1, &mVBState);
    glDeleteBuffers(VB_COUNT, mVB);
    glDeleteProgram(mProgram);
}

float* RendererES3::mapOffsetBuf() {
    glBindBuffer(GL_ARRAY_BUFFER, mVB[VB_OFFSET]);
    return (float*)glMapBufferRange(GL_ARRAY_BUFFER,
            0, MAX_INSTANCES * 2*sizeof(float),
            GL_MAP_WRITE_BIT | GL_MAP_INVALIDATE_BUFFER_BIT);
}

void RendererES3::unmapOffsetBuf() {
    glUnmapBuffer(GL_ARRAY_BUFFER);
}

float* RendererES3::mapTransformBuf() {
    glBindBuffer(GL_ARRAY_BUFFER, mVB[VB_SCALEROT]);
    return (float*)glMapBufferRange(GL_ARRAY_BUFFER,
            0, MAX_INSTANCES * 4*sizeof(float),
            GL_MAP_WRITE_BIT | GL_MAP_INVALIDATE_BUFFER_BIT);
}

void RendererES3::unmapTransformBuf() {
    glUnmapBuffer(GL_ARRAY_BUFFER);
}

void RendererES3::draw(unsigned int numInstances) {
    glUseProgram(mProgram);
    glBindVertexArray(mVBState);
    glDrawArraysInstanced(GL_TRIANGLE_STRIP, 0, 4, numInstances);
    static float fTime = 0.0f;
    fTime += 0.1f;
    ImGui_ImplAndroidGL3_NewFrame(fTime, ImVec2(0.0f, 0.0f), false);
	if (ImGui::Begin("VIP|EnemyFiles | UnityGame", NULL, ImVec2(800, 600))
	{
		ImGui::SetWindowFontScale(3.0f);
		if (ImGui::BeginTabBar("Cheat"))
		{
			if (ImGui::Checkbox("Can Shoot", &canShootEnabled))
			{
				if (canShootEnabled)
				{
					get_canShoot = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x10948D4, "01 00 A0 E3 1E FF 2F E1");
					if (get_canShoot.Modify())
					{
						KITTY_LOGI("get_canShoot has been modified successfully");
						KITTY_LOGI("Current Bytes: %s", get_canShoot.get_CurrBytes().c_str());
					}
				}
				else
				{
					// restore & print bytes
					if (get_canShoot.Restore())
					{
						KITTY_LOGI("get_canShoot has been restored successfully");
						KITTY_LOGI("Current Bytes: %s", get_canShoot.get_CurrBytes().c_str());
					}
				}
			}
			ImGui::EndTabBar();
		}

		if (ImGui::BeginTabBar("MEMORY DUMP"))
		{
			std::string dumpFolder = KittyUtils::getExternalStorage();
			if (ImGui::Checkbox("Dumper il2cpp", &isDumped))
			{
				if (isDumped)
				{
					// dump memory elf
					std::string sodumpPath = dumpFolder + "/il2cpp_dump.so";
					bool elfDumped = kittyMemMgr.dumpMemELF(il2cppBase, sodumpPath);
					KITTY_LOGI("il2cpp so dump = %d", elfDumped ? 1 : 0);

					// dump memory file
					std::string datdumpPath = dumpFolder + "/global-metadata.dat";
					bool datDumped = kittyMemMgr.dumpMemFile("global-metadata.dat", datdumpPath);
					KITTY_LOGI("metadata dump = %d", datDumped ? 1 : 0);
				}
			}
			ImGui::EndTabBar();
		}
		ImGui::End();
		ImGui::Render();
	}
}
