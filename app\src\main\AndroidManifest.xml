<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          package="com.android.gles3jni">
  <uses-feature android:glEsVersion="0x00030000"/>
  <application
      android:allowBackup="false"
      android:fullBackupContent="false"
      android:icon="@mipmap/ic_launcher"
      android:label="@string/gles3jni_activity">
    <activity android:name="GLES3JNIActivity"
              android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
              android:launchMode="singleTask"
              android:configChanges="orientation|keyboardHidden">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
    </activity>
  </application>
</manifest>
