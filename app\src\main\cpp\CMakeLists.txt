cmake_minimum_required(VERSION 3.4.1)

set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -fno-rtti -fno-exceptions -Wall")
if (${ANDROID_PLATFORM_LEVEL} LESS 12)
  message(FATAL_ERROR "OpenGL 2 is not supported before API level 11 \
                      (currently using ${ANDROID_PLATFORM_LEVEL}).")
  return()
elseif (${ANDROID_PLATFORM_LEVEL} LESS 18)
  add_definitions("-DDYNAMIC_ES3")
  set(GL3STUB_SRC gl3stub.c)
  set(OPENGL_LIB GLESv2)
else ()
  set(OPENGL_LIB GLESv3)
endif (${ANDROID_PLATFORM_LEVEL} LESS 12)

set(KITTYMEMORYEX_PATH ${CMAKE_CURRENT_SOURCE_DIR}/KittyMemoryEx)
file(GLOB KITTYMEMORYEX_SRC ${KITTYMEMORYEX_PATH}/*.cpp)
set(KEYSTONE_LIB ${KITTYMEMORYEX_PATH}/Deps/Keystone/libs-android/${CMAKE_ANDROID_ARCH_ABI}/libkeystone.a)

add_library(imGui SHARED
    ./imGui/imgui.cpp
    ./imGui/imgui_draw.cpp
    ./imGui/imgui_demo.cpp
    ./imGui/imgui_impl_android_gl2.cpp
    )
target_include_directories(imGui PRIVATE
    ./imGui)

add_library(gles3jni SHARED
            ${GL3STUB_SRC}
            gles3jni.cpp
            RendererES3.cpp
			${KITTYMEMORYEX_SRC})
target_include_directories(gles3jni PRIVATE
    ./imGui
	${KITTYMEMORYEX_PATH})

target_link_libraries(imGui
    android
    EGL
    GLESv3
    log)

target_link_libraries(gles3jni
            imGui
            ${OPENGL_LIB}
            android
            EGL
            log
            m
			${KEYSTONE_LIB})
