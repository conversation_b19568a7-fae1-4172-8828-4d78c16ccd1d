apply plugin: 'com.android.application'

def platformVersion = 24      // openGLES 3.2 min api level
// def platformVersion = 18    //openGLES 3 min api level
// def platformVersion = 12    //openGLES 2 min api level

android {
    compileSdkVersion 25

    defaultConfig {
        applicationId 'com.android.gles3jni'
        minSdkVersion "${platformVersion}"
        targetSdkVersion 25
        externalNativeBuild {
            cmake {
                arguments '-DANDROID_STL=c++_static'
            }
        }
    }
    buildTypes {
        release {
            minifyEnabled = false
            proguardFiles getDefaultProguardFile('proguard-android.txt'),
                          'proguard-rules.pro'
        }
    }
    externalNativeBuild {
        cmake {
            path 'src/main/cpp/CMakeLists.txt'
        }
    }
}
